{"name": "wmx-advisor-desktop", "private": true, "description": "", "version": "0.0.1", "license": "MIT", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "dotenv -- turbo run build", "start": "dotenv -- turbo run start", "dev": "dotenv -- turbo run dev --parallel", "graph": "turbo run build --graph", "dev-storybook": "turbo run dev-storybook", "build-storybook": "turbo run build-storybook", "test-storybook": "test-storybook", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean && rimraf node_modules", "prepare": "husky install", "format": "prettier --write \"**/README.md\" \"**/src/**/*.{js,jsx,ts,tsx,json}\"", "gen-code": "turbo gen"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": "prettier --write"}, "engines": {"node": ">=22.0.0", "npm": ">=10.2.0"}, "devDependencies": {"@tailwindcss/line-clamp": "^0.4.4", "@turbo/gen": "^1.9.7", "@types/react-speech-recognition": "^3.9.5", "eslint": "^8.56.0", "eslint-config-custom": "*", "husky": "^8.0.3", "lint-staged": "^13.1.0", "prettier": "^2.8.3", "rimraf": "^4.1.1", "swiper": "^11.1.4", "turbo": "^1.10.9"}, "packageManager": "npm@10.2.0", "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@tailwindcss/postcss": "^4.1.12", "apexcharts": "^4.0.0", "chart.js": "^4.4.3", "classnames": "^2.3.2", "clipboard": "^2.0.11", "dayjs": "^1.11.11", "flowbite": "^2.4.1", "html-to-text": "^9.0.5", "jspdf": "^2.5.1", "pg": "^8.12.0"}}